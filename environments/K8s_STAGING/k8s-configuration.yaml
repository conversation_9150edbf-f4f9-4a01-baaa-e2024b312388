apiVersion: v1
kind: Secret
metadata:
  name: profile-database
  namespace: pg-ledger-staging
type: Opaque
stringData:
  sys.database.host: "gl-db-tst-stg.csfhkewhn9xz.ca-central-1.rds.amazonaws.com"
  sys.database.read.host: "gl-db-tst-stg-ro.csfhkewhn9xz.ca-central-1.rds.amazonaws.com"
  sys.database.name: "gl_db_staging"
  sys.database.schema: "relationship"
  sys.database.username: "relationship_staging_user"
  sys.database.password: "thU3jMfcIN1EEZSNu1"
---
apiVersion: v1
kind: Secret
metadata:
  name: account-database
  namespace: pg-ledger-staging
type: Opaque
stringData:
  sys.database.host: "gl-db-tst-stg.csfhkewhn9xz.ca-central-1.rds.amazonaws.com"
  sys.database.read.host: "gl-db-tst-stg-ro.csfhkewhn9xz.ca-central-1.rds.amazonaws.com"
  sys.database.name: "gl_db_staging"
  sys.database.schema: "ledger_account"
  sys.database.username: "account_staging_user"
  sys.database.password: "BogndjORCREE9sWKU8"
---
apiVersion: v1
kind: Secret
metadata:
  name: transaction-database
  namespace: pg-ledger-staging
type: Opaque
stringData:
  sys.database.host: "gl-db-tst-stg.csfhkewhn9xz.ca-central-1.rds.amazonaws.com"
  sys.database.read.host: "gl-db-tst-stg-ro.csfhkewhn9xz.ca-central-1.rds.amazonaws.com"
  sys.database.name: "gl_db_staging"
  sys.database.schema: "ledger_transaction"
  sys.database.username: "transaction_staging_user"
  sys.database.password: "Mo5O0TAQt!%Q^ubUTb"

---
apiVersion: v1
kind: Secret
metadata:
  name: kafka
  namespace: pg-ledger-staging
type: Opaque
stringData:
  sys.kafka.host: "b-1.glkafkaclusterstagi.h9k7qt.c4.kafka.ca-central-1.amazonaws.com:9096,b-2.glkafkaclusterstagi.h9k7qt.c4.kafka.ca-central-1.amazonaws.com:9096"
  sys.kafka.sasl.username: "AmazonMSK_ledger-kafka-user-staging"
  sys.kafka.sasl.password: "}zKfR?S.Djo9>ws4/YfZ?O"

---
apiVersion: v1
kind: Secret
metadata:
  name: aws-secret
  namespace: pg-ledger-staging
type: Opaque
stringData:
  aws.access.key: "********************"
  aws.secret.key: "MlIZQe/t/XGqVa1BAjx9e9e3gwXc0AFca2gmz5MQ"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: application-dependencies
  namespace: pg-ledger-staging
data:
  sys.profile.url: "http://profile-service.pg-ledger-staging.svc.cluster.local:8080/v1/internal/profile"
  sys.account.url: "http://account-service.pg-ledger-staging.svc.cluster.local:8080/v1/internal/account"
  sys.account.validate.url: "http://account-service.pg-ledger-staging.svc.cluster.local:8080/v1/internal/account/validate"
  sys.transaction.url: "http://transaction-service.pg-ledger-staging.svc.cluster.local:8080/v1/internal/transaction/balance"
  sys.accounts.retrieve.url: "http://account-service.pg-ledger-staging.svc.cluster.local:8080/v1/internal/accounts/all"
  sys.rollback.internal.url: "http://transaction-service.pg-ledger-staging.svc.cluster.local:8080/v1/internal/ledger/transaction/rollback"
  app.balance-snapshot.schedule: "0 0 1,6,18 ? * *"
  app.rollback-transactions.schedule: "0 0 4 * * *"
  app.rollback-transactions.expiry.time: "5"
  app.transactions.expiry.day: "32"
  app.instructions.fetch.limit: "250"
  app.use.store.procedure.for.sum: "true"
  app.ssl.enable: "true"
  app.kafka.topic: "PENDING_TRANSACTIONS_STAGING"
  app.kafka.topic.v2: "PENDING_TRANSACTIONS_V2_STAGING"
  app.kafka.error.topic: "MANUAL_INTERVENTION_TRANSACTIONS_STAGING"
  app.kafka.failed.status.topic: "FAILED_TRANSACTIONS_STAGING"
  app.kafka.consumer.group.id: "ASYNCHRONOUS_API_STAGING"
  app.kafka.backoff.maxfailure: "3"
  app.kafka.backoff.interval: "2000"
  app.kafka.max.poll.idle: "1000"
  app.kafka.max.poll.interval: "700000"
  app.kafka.max.poll.records: "100"
  app.kafka.listener.number: "1"
  sys.gl.transaction.url: "http://transaction-service.pg-ledger-staging.svc.cluster.local:8080/v1/ledger/transaction"
  app.transaction.validation-service.timeout.connection: "2000"
  app.transaction.validation-service.timeout.read: "10000"
  app.transaction.api.timeout.connection: "5000"
  app.transaction.api.timeout.read: "10000"
  app.redis.read.timeout: "120"
  app.redis.connection.timeout: "30"
  app.redis.cache.expiry: "5"
  app.db.maximum.pool.size: "10"
  app.db.connection.timeout: "2000"
  app.db.minimum.idle: "2"
  app.account.validation-service.timeout.connection: "2000"
  app.account.validation-service.timeout.read: "10000"
  app.health.eks-name-space: "pg-ledger-staging"
  app.health.cloud-watch-log-group: "/non-prod/staging/general-ledger/eks/gl-eks-main-staging/application"
  app.jwt.token.check.disable: "false"
  app.async.transaction.producer.v2: "true"
  app.kafka-start.url: "http://aef5b5b3fe058440ebbb99d7fdc26408-a87d2a851a18d829.elb.ca-central-1.amazonaws.com:8080/v1/util/start-kafka-listener"
  app.kafka-stop.url: "http://aef5b5b3fe058440ebbb99d7fdc26408-a87d2a851a18d829.elb.ca-central-1.amazonaws.com:8080/v1/util/stop-kafka-listener"
  app.log.level.root: "WARN"
  app.log.level.com.peoplestrust: "WARN"
  app.log.level.hibernate: "WARN"
  app.log.level.api.payload: "WARN"
  app.log.level.perf: "WARN"
  app.log.level.flow: "WARN"
